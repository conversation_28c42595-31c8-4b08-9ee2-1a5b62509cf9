<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { Link, usePage } from "@inertiajs/vue3";
import PreviewSection from '@/Components/PreviewSection.vue';
import { defineProps, computed, getCurrentInstance } from "vue";
import ListOption from '@/Components/ListOption.vue';
import ProfileSection from '@/Components/ProfileSection.vue';
import ButtonGroupOption from '@/Components/ButtonGroupOption.vue';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from '@/components/ui/table';
import { differenceInMonths, differenceInWeeks, format } from 'date-fns';

const props = defineProps({
  Product: Object | Array,
});

const instance = getCurrentInstance();
const zataCurrency = instance.appContext.config.globalProperties.$zataCurrency;

const formatExpireDate = (expireDate) => {
  if (!expireDate) return 'N/A';

  const today = new Date(); 
  const expiry = new Date(expireDate);
  
  const monthsDiff = differenceInMonths(expiry, today);
  const weeksDiff = differenceInWeeks(expiry, today);

  if (monthsDiff >= 12) {
    return format(expiry, 'M/d/yyyy'); 
  } else if (monthsDiff >= 1) {
    return `${monthsDiff} month${monthsDiff > 1 ? 's' : ''}`;
  } else if (weeksDiff >= 1) {
    return `${weeksDiff} week${weeksDiff > 1 ? 's' : ''}`;
  } else {
    const daysDiff = Math.round((expiry - today) / (1000 * 60 * 60 * 24));
    return daysDiff >= 0 ? `${daysDiff} day${daysDiff !== 1 ? 's' : ''}` : 'Expired';
  }
};

const tableHeaders = [
  "Batch Number",
  "Current Stock",
  "Discount Rate",
  "Expire Date",
  "Purchase Price",
  "Sales Price",
  "Status",
  "Actions", 
];

const tableRows = computed(() => {
  return props.Product.productDetails.map((product) => ({
    id: product.id,
    batchNumber: product.batchNumber,
    currentStock: product.currentStock,
    discountRate: product.discountRate ? `${product.discountRate}%` : 'N/A',
    expireDate: formatExpireDate(product.expireDate),
    purchasePrice: zataCurrency(product.purchasePrice),
    salesPrice: zataCurrency(product.salesPrice),
    status: product.status,
  }));
});

const details = computed(() => {
  return [
    { title: "Total Stock", value: props.Product.totalStock },
    { title: "Conversion Factor", value: props.Product.conversionFactor },
    { title: "Item Code", value: props.Product.itemCode },
    { title: "Tax", value: props.Product.tax },
    { title: "Quantity Unit", value: props.Product.quantityUnit },
    { title: "Packaging Unit", value: props.Product.packagingUnit },
  ];
});
</script>

<template>
    <AppLayout :title="Product.name">
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ Product.name }}
            </h2>
        </template>

        <PreviewSection>
            <template #content>
                <ProfileSection :image="Product.image" :title="Product.name"
                    :description="Product.branchProductCategory" />

                <ButtonGroupOption>
                    <Button as-child>
                        <Link :href="route('product.edit', Product.id)">
                            Edit
                        </Link>
                    </Button>

                    <Button as-child variant="secondary"  v-if="Product.hasStock === 'yes'  || Product.hasStock === null">
                        <Link :href="route('product.stockIn', Product.id)">
                            Load
                        </Link>
                    </Button>

                    <Button as-child variant="secondary" v-if="Product.hasStock === 'yes'  || Product.hasStock === null">
                        <Link :href="route('product.stockOut', Product.id)">
                            Unload
                        </Link>
                    </Button>

                    <Button as-child variant="secondary">
                        <Link :href="route('product.stockTransfer', Product.id)">
                            Transfer
                        </Link>
                    </Button>
                </ButtonGroupOption>

                <ListOption :details="details" title="More Details" />
            </template>
            <template #deatils>
                <div class="mt-6">
          <h3 class="text-lg font-semibold text-gray-800 mb-4">Batch Details</h3>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead v-for="header in tableHeaders" :key="header" class="text-left">
                  {{ header }}
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow v-for="row in tableRows" :key="row.batchNumber">
                <TableCell>{{ row.batchNumber }}</TableCell>
                <TableCell>{{ row.currentStock }}</TableCell>
                <TableCell>{{ row.discountRate }}</TableCell>
                <TableCell>{{ row.expireDate }}</TableCell>
                <TableCell>{{ row.purchasePrice }}</TableCell>
                <TableCell>{{ row.salesPrice }}</TableCell>
                <TableCell>{{ row.status }}</TableCell>
                <TableCell>
                  <Link
                    :href="route('product.editBatch', [Product.id, row.id])"
                    class="text-zata-primary-light hover:underline"
                  >
                    Update
                  </Link>
                </TableCell>
              </TableRow>
              <TableRow v-if="!tableRows.length">
                <TableCell colspan="8" class="text-center text-gray-500"> <!-- Updated colspan to 8 -->
                  No batch data available
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>
            </template>
        </PreviewSection>
        <template #sidenav />
        <template #footer />
    </AppLayout>
</template>
