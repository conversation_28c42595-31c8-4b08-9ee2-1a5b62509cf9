<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { ref, computed } from 'vue';
import { useForm } from '@inertiajs/vue3';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  RadioGroup,
  RadioGroupItem,
} from '@/components/ui/radio-group';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { format } from 'date-fns';

const props = defineProps({
  Product: Object,
});

const form = useForm({
  name: props.Product.name,
  quantity: 0,
  description: '',
  movingUnit: 'sub',
  soldInSubUnit: props.Product.soldInSubUnit,
  salePrice: props.Product.salesPrice,
  purchasePrice: props.Product.purchasePrice,
  batchNumber: '',
  expireDate: null,
  discountRate: 0,
});

const expireDate = ref(null);

// Compute minimum date (today)
const minDate = computed(() => {
  const today = new Date();
  return today.toISOString().split('T')[0];
});

function updateProduct() {
  form.expireDate = expireDate.value ? format(expireDate.value, 'yyyy-MM-dd') : null;
  
  // Validate expiry date before submission
  if (form.expireDate && new Date(form.expireDate) < new Date(minDate.value)) {
    form.errors.expireDate = 'Please select a future date';
    return;
  }
  
  form.put(route('product.stockInStore', props.Product.id), {});
}

const options = computed(() => [
  { value: 'sub', label: `Sub Units / ${props.Product.quantityUnit}` },
  { value: 'main', label: `Main Units / ${props.Product.packagingUnit}` },
]);
</script>

<template>
  <AppLayout title="Stock In">
    <template #header>
      <h2 class="font-semibold text-2xl text-gray-900 leading-tight">
        Stock In Product
      </h2>
    </template>

    <div class="py-6 px-4 sm:px-6 lg:px-8">
      <Card class="shadow-lg border border-gray-200 max-w-3xl mx-auto">
        <CardHeader class="bg-gray-50 border-b">
          <CardTitle class="text-xl font-semibold text-gray-800">
            Load <span class="font-bold">{{ Product.name }}</span> Stock
          </CardTitle>
        </CardHeader>
        <CardContent class="p-6">
          <form @submit.prevent="updateProduct" class="space-y-6">
            <div class="space-y-2" v-if="Product.hasStock === 'yes'">
              <Label for="quantity">Quantity</Label>
              <Input id="quantity" v-model="form.quantity" type="number" />
              <p v-if="form.errors.quantity" class="text-red-500 text-sm mt-1">{{ form.errors.quantity }}</p>
            </div>

            <div v-if="form.soldInSubUnit" class="space-y-2">
              <Label>Load Unit</Label>
              <RadioGroup v-model="form.movingUnit" class="flex space-x-4">
                <div v-for="option in options" :key="option.value" class="flex items-center space-x-2">
                  <RadioGroupItem :value="option.value" :id="`moving-unit-${option.value}`" />
                  <Label :for="`moving-unit-${option.value}`">{{ option.label }}</Label>
                </div>
              </RadioGroup>
              <p v-if="form.errors.movingUnit" class="text-red-500 text-sm mt-1">{{ form.errors.movingUnit }}</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="space-y-2">
                <Label for="salePrice">Sale Price</Label>
                <Input id="salePrice" v-model="form.salePrice" type="number" />
                <p v-if="form.errors.salePrice" class="text-red-500 text-sm mt-1">{{ form.errors.salePrice }}</p>
              </div>
              <div class="space-y-2">
                <Label for="purchasePrice">Purchase Price</Label>
                <Input id="purchasePrice" v-model="form.purchasePrice" type="number" />
                <p v-if="form.errors.purchasePrice" class="text-red-500 text-sm mt-1">{{ form.errors.purchasePrice }}</p>
              </div>
            </div>

            <div class="space-y-2">
              <Label for="discountRate">Discount Rate</Label>
              <Input id="discountRate" v-model="form.discountRate" type="number" />
              <p v-if="form.errors.discountRate" class="text-red-500 text-sm mt-1">{{ form.errors.discountRate }}</p>
            </div>

            <div class="space-y-2" v-if="Product.hasStock === 'yes' || Product.hasStock === null">
              <Label for="batchNumber">Batch Number</Label>
              <Input id="batchNumber" v-model="form.batchNumber" type="text" />
              <p v-if="form.errors.batchNumber" class="text-red-500 text-sm mt-1">{{ form.errors.batchNumber }}</p>
            </div>

            <div class="space-y-2" v-if="Product.hasStock === 'yes'  || Product.hasStock === null">
              <Label>Expiry Date</Label>
              <Input 
                id="expireDate" 
                v-model="expireDate" 
                type="date" 
                :min="minDate"
              />
              <p v-if="form.errors.expireDate" class="text-red-500 text-sm mt-1">{{ form.errors.expireDate }}</p>
            </div>

            <div class="flex justify-end gap-4">
              <Button 
                type="submit" 
                :disabled="form.processing"
                :class="{ 'opacity-25': form.processing }"
              >
                Load Stock
              </Button>
            </div>

            <p v-if="form.recentlySuccessful" class="text-green-600 text-sm">
              Successfully updated.
            </p>
          </form>
        </CardContent>
      </Card>

      <div class="flex justify-center mt-6" v-if="!Product.hasStock === 'no' && Product.hasStock === null">
        <p class="text-center text-gray-500 text-lg">
          Current stock: <span class="font-bold">{{ Product.totalStock }}</span>
        </p>
      </div>
    </div>

    <template #sidenav />
    <template #footer />
  </AppLayout>
</template>