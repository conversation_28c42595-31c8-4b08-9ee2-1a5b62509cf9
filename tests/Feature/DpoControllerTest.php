<?php

use App\Services\DpoPaymentService;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Config;

beforeEach(function () {
    // Set up DPO configuration for testing
    Config::set('dpo.base_url', 'https://secure.3gdirectpay.com/API/v6/');
    Config::set('dpo.company_token', 'test_company_token_123456789');
    Config::set('dpo.default_currency', 'USD');
    Config::set('dpo.default_ptl', 5);

    $this->dpoBaseUrl = 'https://secure.3gdirectpay.com/API/v6/';
    $this->createPaymentUrl = '/payments/create';
    $this->verifyPaymentUrl = '/payments/verify';
    $this->mobilePaymentUrl = '/payments/mobile';
    $this->mobileOptionsUrl = '/payments/mobile-options';
    $this->refundUrl = '/payments/refund';
    $this->balanceUrl = '/payments/balance';
});

it('can create payment token successfully', function () {
    $payload = [
        'amount' => 100.50,
        'currency' => 'USD',
        'company_ref' => 'TEST_REF_001',
        'redirect_url' => 'https://example.com/success',
        'back_url' => 'https://example.com/cancel',
        'service_type' => 1,
        'service_description' => 'Test Service Payment',
        'service_date' => '2024/12/01 10:30'
    ];

    $dpoResponse = [
        'Result' => '000',
        'ResultExplanation' => 'Transaction token created successfully',
        'TransToken' => 'TEST_TOKEN_123456789',
        'TransRef' => 'TEST_REF_001'
    ];

    Http::fake([
        $this->dpoBaseUrl => Http::response(buildXmlResponse($dpoResponse))
    ]);

    $response = $this->postJson($this->createPaymentUrl, $payload);

    $response->assertStatus(200)
        ->assertJsonStructure([
            'success',
            'token',
            'reference',
            'payment_url'
        ])
        ->assertJson([
            'success' => true,
            'token' => 'TEST_TOKEN_123456789',
            'reference' => 'TEST_REF_001'
        ]);

    // Verify the HTTP request was made with correct XML structure
    $recorded = Http::recorded();
    expect($recorded)->toHaveCount(1);

    $requestBody = $recorded[0][0]->body();
    expect($requestBody)->toContain('<CompanyToken>test_company_token_123456789</CompanyToken>');
    expect($requestBody)->toContain('<Request>createToken</Request>');
    expect($requestBody)->toContain('<PaymentAmount>100.50</PaymentAmount>');
    expect($requestBody)->toContain('<PaymentCurrency>USD</PaymentCurrency>');
    expect($requestBody)->toContain('<CompanyRef>TEST_REF_001</CompanyRef>');
})->skip();

it('validates required fields for payment creation', function (array $invalidPayload, string $expectedError) {
    $response = $this->postJson($this->createPaymentUrl, $invalidPayload);

    $response->assertStatus(422)
        ->assertJsonValidationErrors($expectedError);
})->with([
    'missing amount' => [
        ['currency' => 'USD', 'company_ref' => 'TEST', 'service_type' => 1, 'service_description' => 'Test'],
        'amount'
    ],
    'invalid amount' => [
        ['amount' => 0, 'currency' => 'USD', 'company_ref' => 'TEST', 'service_type' => 1, 'service_description' => 'Test'],
        'amount'
    ],
    'missing currency' => [
        ['amount' => 100, 'company_ref' => 'TEST', 'service_type' => 1, 'service_description' => 'Test'],
        'currency'
    ],
    'invalid currency length' => [
        ['amount' => 100, 'currency' => 'USDD', 'company_ref' => 'TEST', 'service_type' => 1, 'service_description' => 'Test'],
        'currency'
    ],
    'missing company_ref' => [
        ['amount' => 100, 'currency' => 'USD', 'service_type' => 1, 'service_description' => 'Test'],
        'company_ref'
    ],
    'missing service_type' => [
        ['amount' => 100, 'currency' => 'USD', 'company_ref' => 'TEST', 'service_description' => 'Test'],
        'service_type'
    ],
    'missing service_description' => [
        ['amount' => 100, 'currency' => 'USD', 'company_ref' => 'TEST', 'service_type' => 1],
        'service_description'
    ]
])->skip();

it('handles DPO API errors gracefully', function () {
    $payload = [
        'amount' => 100.50,
        'currency' => 'USD',
        'company_ref' => 'TEST_REF_001',
        'service_type' => 1,
        'service_description' => 'Test Service Payment'
    ];

    $dpoErrorResponse = [
        'Result' => '801',
        'ResultExplanation' => 'Request missing company token'
    ];

    Http::fake([
        $this->dpoBaseUrl => Http::response($this->buildXmlResponse($dpoErrorResponse))
    ]);

    $response = $this->postJson($this->createPaymentUrl, $payload);

    $response->assertStatus(400)
        ->assertJson([
            'success' => false,
            'error' => 'Request missing company token: Request missing company token'
        ]);
})->skip();

it('handles HTTP errors from DPO API', function () {
    $payload = [
        'amount' => 100.50,
        'currency' => 'USD',
        'company_ref' => 'TEST_REF_001',
        'service_type' => 1,
        'service_description' => 'Test Service Payment'
    ];

    Http::fake([
        $this->dpoBaseUrl => Http::response('Server Error', 500)
    ]);

    $response = $this->postJson($this->createPaymentUrl, $payload);

    $response->assertStatus(500)
        ->assertJson([
            'success' => false
        ])
        ->assertJsonStructure([
            'success',
            'error'
        ]);
})->skip();

it('can verify payment with transaction token', function () {
    $payload = [
        'transaction_token' => 'TEST_TOKEN_123456789'
    ];

    $dpoResponse = [
        'Result' => '000',
        'ResultExplanation' => 'Transaction paid successfully',
        'CustomerName' => 'John Doe',
        'TransactionAmount' => '100.50',
        'TransactionCurrency' => 'USD',
        'FraudAlert' => 'N',
        'CustomerPhone' => '+1234567890',
        'CustomerCountry' => 'US'
    ];

    Http::fake([
        $this->dpoBaseUrl => Http::response($this->buildXmlResponse($dpoResponse))
    ]);

    $response = $this->postJson($this->verifyPaymentUrl, $payload);

    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'paid' => true,
            'result_code' => '000',
            'result_explanation' => 'Transaction paid successfully',
            'customer_name' => 'John Doe',
            'transaction_amount' => '100.50',
            'transaction_currency' => 'USD',
            'fraud_alert' => 'N',
            'customer_phone' => '+1234567890',
            'customer_country' => 'US'
        ]);

    // Verify the HTTP request was made with correct XML structure
    $recorded = Http::recorded();
    expect($recorded)->toHaveCount(1);

    $requestBody = $recorded[0][0]->body();
    expect($requestBody)->toContain('<Request>verifyToken</Request>');
    expect($requestBody)->toContain('<TransactionToken>TEST_TOKEN_123456789</TransactionToken>');
})->skip();

it('can verify payment with company reference', function () {
    $payload = [
        'company_ref' => 'TEST_REF_001'
    ];

    $dpoResponse = [
        'Result' => '900',
        'ResultExplanation' => 'Transaction not paid yet'
    ];

    Http::fake([
        $this->dpoBaseUrl => Http::response($this->buildXmlResponse($dpoResponse))
    ]);

    $response = $this->postJson($this->verifyPaymentUrl, $payload);

    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'paid' => false,
            'result_code' => '900',
            'result_explanation' => 'Transaction not paid yet'
        ]);

    // Verify the HTTP request was made with correct XML structure
    $recorded = Http::recorded();
    expect($recorded)->toHaveCount(1);

    $requestBody = $recorded[0][0]->body();
    expect($requestBody)->toContain('<Request>verifyToken</Request>');
    expect($requestBody)->toContain('<CompanyRef>TEST_REF_001</CompanyRef>');
})->skip();

it('validates verification request requires either token or company ref', function () {
    $response = $this->postJson($this->verifyPaymentUrl, []);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['transaction_token', 'company_ref']);
})->skip();

it('can process mobile payment successfully', function () {
    $payload = [
        'transaction_token' => 'TEST_TOKEN_123456789',
        'phone_number' => '1234567890',
        'mno' => 'MTN',
        'mno_country' => 'RW'
    ];

    $dpoResponse = [
        'StatusCode' => '200',
        'instructions' => 'Please check your phone for payment prompt',
        'RedirectOption' => '0'
    ];

    Http::fake([
        $this->dpoBaseUrl => Http::response($this->buildXmlResponse($dpoResponse))
    ]);

    $response = $this->postJson($this->mobilePaymentUrl, $payload);

    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'status_code' => '200',
            'instructions' => 'Please check your phone for payment prompt',
            'redirect_required' => false
        ]);

    // Verify the HTTP request was made with correct XML structure
    $recorded = Http::recorded();
    expect($recorded)->toHaveCount(1);

    $requestBody = $recorded[0][0]->body();
    expect($requestBody)->toContain('<Request>ChargeTokenMobile</Request>');
    expect($requestBody)->toContain('<TransactionToken>TEST_TOKEN_123456789</TransactionToken>');
    expect($requestBody)->toContain('<PhoneNumber>1234567890</PhoneNumber>');
    expect($requestBody)->toContain('<MNO>MTN</MNO>');
    expect($requestBody)->toContain('<MNOcountry>RW</MNOcountry>');
})->skip();

it('validates mobile payment required fields', function (array $invalidPayload, string $expectedError) {
    $response = $this->postJson($this->mobilePaymentUrl, $invalidPayload);

    $response->assertStatus(422)
        ->assertJsonValidationErrors($expectedError);
})->with([
    'missing transaction_token' => [
        ['phone_number' => '1234567890', 'mno' => 'MTN', 'mno_country' => 'RW'],
        'transaction_token'
    ],
    'missing phone_number' => [
        ['transaction_token' => 'TEST_TOKEN', 'mno' => 'MTN', 'mno_country' => 'RW'],
        'phone_number'
    ],
    'missing mno' => [
        ['transaction_token' => 'TEST_TOKEN', 'phone_number' => '1234567890', 'mno_country' => 'RW'],
        'mno'
    ],
    'missing mno_country' => [
        ['transaction_token' => 'TEST_TOKEN', 'phone_number' => '1234567890', 'mno' => 'MTN'],
        'mno_country'
    ]
])->skip();

it('can get mobile payment options', function () {
    $dpoResponse = [
        'paymentoptions' => [
            'option' => [
                ['name' => 'MTN Mobile Money', 'code' => 'MTN'],
                ['name' => 'Airtel Money', 'code' => 'AIRTEL']
            ]
        ]
    ];

    Http::fake([
        $this->dpoBaseUrl => Http::response($this->buildXmlResponse($dpoResponse))
    ]);

    $response = $this->getJson($this->mobileOptionsUrl . '?transaction_token=TEST_TOKEN_123456789');

    $response->assertStatus(200)
        ->assertJson([
            'success' => true
        ])
        ->assertJsonStructure([
            'success',
            'payment_options'
        ]);

    // Verify the HTTP request was made with correct XML structure
    $recorded = Http::recorded();
    expect($recorded)->toHaveCount(1);

    $requestBody = $recorded[0][0]->body();
    expect($requestBody)->toContain('<Request>GetMobilePaymentOptions</Request>');
    expect($requestBody)->toContain('<TransactionToken>TEST_TOKEN_123456789</TransactionToken>');
})->skip();

it('validates mobile options request requires transaction token', function () {
    $response = $this->getJson($this->mobileOptionsUrl);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['transaction_token']);
})->skip();

it('can process refund successfully', function () {
    $payload = [
        'transaction_token' => 'TEST_TOKEN_123456789',
        'refund_amount' => 50.25,
        'refund_details' => 'Customer requested refund',
        'refund_ref' => 'REFUND_001',
        'refund_approval' => true
    ];

    $dpoResponse = [
        'Result' => '000',
        'ResultExplanation' => 'Refund processed successfully'
    ];

    Http::fake([
        $this->dpoBaseUrl => Http::response($this->buildXmlResponse($dpoResponse))
    ]);

    $response = $this->postJson($this->refundUrl, $payload);

    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'result' => 'Refund processed successfully'
        ]);

    // Verify the HTTP request was made with correct XML structure
    $recorded = Http::recorded();
    expect($recorded)->toHaveCount(1);

    $requestBody = $recorded[0][0]->body();
    expect($requestBody)->toContain('<Request>refundToken</Request>');
    expect($requestBody)->toContain('<TransactionToken>TEST_TOKEN_123456789</TransactionToken>');
    expect($requestBody)->toContain('<refundAmount>50.25</refundAmount>');
    expect($requestBody)->toContain('<refundDetails>Customer requested refund</refundDetails>');
    expect($requestBody)->toContain('<refundRef>REFUND_001</refundRef>');
    expect($requestBody)->toContain('<refundApproval>1</refundApproval>');
})->skip();

it('validates refund required fields', function (array $invalidPayload, string $expectedError) {
    $response = $this->postJson($this->refundUrl, $invalidPayload);

    $response->assertStatus(422)
        ->assertJsonValidationErrors($expectedError);
})->with([
    'missing transaction_token' => [
        ['refund_amount' => 50.25, 'refund_details' => 'Test refund'],
        'transaction_token'
    ],
    'missing refund_amount' => [
        ['transaction_token' => 'TEST_TOKEN', 'refund_details' => 'Test refund'],
        'refund_amount'
    ],
    'invalid refund_amount' => [
        ['transaction_token' => 'TEST_TOKEN', 'refund_amount' => 0, 'refund_details' => 'Test refund'],
        'refund_amount'
    ],
    'missing refund_details' => [
        ['transaction_token' => 'TEST_TOKEN', 'refund_amount' => 50.25],
        'refund_details'
    ]
])->skip();

it('handles failed refund', function () {
    $payload = [
        'transaction_token' => 'TEST_TOKEN_123456789',
        'refund_amount' => 50.25,
        'refund_details' => 'Customer requested refund'
    ];

    $dpoResponse = [
        'Result' => '901',
        'ResultExplanation' => 'Transaction declined'
    ];

    Http::fake([
        $this->dpoBaseUrl => Http::response($this->buildXmlResponse($dpoResponse))
    ]);

    $response = $this->postJson($this->refundUrl, $payload);

    $response->assertStatus(200)
        ->assertJson([
            'success' => false,
            'result' => 'Refund processed'
        ]);
})->skip();

it('can get company balance', function () {
    $dpoResponse = [
        'CompanyBalance' => '1500.75',
        'ExchangeRate' => '1.0'
    ];

    Http::fake([
        $this->dpoBaseUrl => Http::response($this->buildXmlResponse($dpoResponse))
    ]);

    $response = $this->getJson($this->balanceUrl . '?currency=USD');

    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'balance' => '1500.75',
            'exchange_rate' => '1.0',
            'currency' => 'USD'
        ]);

    // Verify the HTTP request was made with correct XML structure
    $recorded = Http::recorded();
    expect($recorded)->toHaveCount(1);

    $requestBody = $recorded[0][0]->body();
    expect($requestBody)->toContain('<Request>getBalance</Request>');
    expect($requestBody)->toContain('<Currency>USD</Currency>');
})->skip();

it('validates balance request requires currency', function () {
    $response = $this->getJson($this->balanceUrl);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['currency']);
})->skip();

it('validates currency format for balance request', function () {
    $response = $this->getJson($this->balanceUrl . '?currency=USDD');

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['currency']);
})->skip();

it('handles service exceptions gracefully', function () {
    // Mock the DpoPaymentService to throw an exception
    $this->mock(DpoPaymentService::class, function ($mock) {
        $mock->shouldReceive('formatTransactionData')->andThrow(new \Exception('Service unavailable'));
    });

    $payload = [
        'amount' => 100.50,
        'currency' => 'USD',
        'company_ref' => 'TEST_REF_001',
        'service_type' => 1,
        'service_description' => 'Test Service Payment'
    ];

    $response = $this->postJson($this->createPaymentUrl, $payload);

    $response->assertStatus(500)
        ->assertJson([
            'success' => false,
            'error' => 'Service unavailable'
        ]);
})->skip();

it('handles invalid XML response from DPO', function () {
    $payload = [
        'amount' => 100.50,
        'currency' => 'USD',
        'company_ref' => 'TEST_REF_001',
        'service_type' => 1,
        'service_description' => 'Test Service Payment'
    ];

    Http::fake([
        $this->dpoBaseUrl => Http::response('Invalid XML Response')
    ]);

    $response = $this->postJson($this->createPaymentUrl, $payload);

    $response->assertStatus(500)
        ->assertJson([
            'success' => false
        ]);
})->skip();

it('handles network timeout gracefully', function () {
    $payload = [
        'amount' => 100.50,
        'currency' => 'USD',
        'company_ref' => 'TEST_REF_001',
        'service_type' => 1,
        'service_description' => 'Test Service Payment'
    ];

    Http::fake([
        $this->dpoBaseUrl => function () {
            throw new \Illuminate\Http\Client\ConnectionException('Connection timeout');
        }
    ]);

    $response = $this->postJson($this->createPaymentUrl, $payload);

    $response->assertStatus(500)
        ->assertJson([
            'success' => false
        ]);
})->skip();

it('correctly formats payment URL in response', function () {
    $payload = [
        'amount' => 100.50,
        'currency' => 'USD',
        'company_ref' => 'TEST_REF_001',
        'service_type' => 1,
        'service_description' => 'Test Service Payment'
    ];

    $dpoResponse = [
        'Result' => '000',
        'ResultExplanation' => 'Transaction token created successfully',
        'TransToken' => 'ABC123XYZ789',
        'TransRef' => 'TEST_REF_001'
    ];

    Http::fake([
        $this->dpoBaseUrl => Http::response($this->buildXmlResponse($dpoResponse))
    ]);

    $response = $this->postJson($this->createPaymentUrl, $payload);

    $response->assertStatus(200)
        ->assertJson([
            'payment_url' => 'https://secure.3gdirectpay.com/payv2.php?ID=ABC123XYZ789'
        ]);
})->skip();

it('handles missing DPO company token configuration', function () {
    Config::set('dpo.company_token', null);

    $payload = [
        'amount' => 100.50,
        'currency' => 'USD',
        'company_ref' => 'TEST_REF_001',
        'service_type' => 1,
        'service_description' => 'Test Service Payment'
    ];

    $response = $this->postJson($this->createPaymentUrl, $payload);

    $response->assertStatus(500)
        ->assertJson([
            'success' => false,
            'error' => 'DPO Company Token is required'
        ]);
})->skip();

it('handles different payment statuses correctly', function (string $resultCode, bool $expectedPaid) {
    $payload = [
        'transaction_token' => 'TEST_TOKEN_123456789'
    ];

    $dpoResponse = [
        'Result' => $resultCode,
        'ResultExplanation' => 'Test response'
    ];

    Http::fake([
        $this->dpoBaseUrl => Http::response($this->buildXmlResponse($dpoResponse))
    ]);

    $response = $this->postJson($this->verifyPaymentUrl, $payload);

    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'paid' => $expectedPaid,
            'result_code' => $resultCode
        ]);
})->with([
    'paid status 000' => ['000', true],
    'paid status 001' => ['001', true],
    'unpaid status 900' => ['900', false],
    'declined status 901' => ['901', false],
    'cancelled status 904' => ['904', false]
])->skip();

it('handles mobile payment with redirect option', function () {
    $payload = [
        'transaction_token' => 'TEST_TOKEN_123456789',
        'phone_number' => '1234567890',
        'mno' => 'MTN',
        'mno_country' => 'RW'
    ];

    $dpoResponse = [
        'StatusCode' => '200',
        'instructions' => 'Redirect required for payment',
        'RedirectOption' => '1'
    ];

    Http::fake([
        $this->dpoBaseUrl => Http::response($this->buildXmlResponse($dpoResponse))
    ]);

    $response = $this->postJson($this->mobilePaymentUrl, $payload);

    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'redirect_required' => true
        ]);
})->skip();

it('processes refund without optional fields', function () {
    $payload = [
        'transaction_token' => 'TEST_TOKEN_123456789',
        'refund_amount' => 25.00,
        'refund_details' => 'Partial refund'
    ];

    $dpoResponse = [
        'Result' => '000',
        'ResultExplanation' => 'Refund processed successfully'
    ];

    Http::fake([
        $this->dpoBaseUrl => Http::response($this->buildXmlResponse($dpoResponse))
    ]);

    $response = $this->postJson($this->refundUrl, $payload);

    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'result' => 'Refund processed successfully'
        ]);

    // Verify optional fields are not included when not provided
    $recorded = Http::recorded();
    $requestBody = $recorded[0][0]->body();
    expect($requestBody)->not->toContain('<refundRef>');
    expect($requestBody)->not->toContain('<refundApproval>1</refundApproval>');
})->skip();

/**
 * Helper function to build XML response for DPO API
 */
function buildXmlResponse(array $data): string
{
    $xml = new \SimpleXMLElement('<API3G/>');

    foreach ($data as $key => $value) {
        if (is_array($value)) {
            $childNode = $xml->addChild($key);
            addArrayToXml($childNode, $value);
        } else {
            $xml->addChild($key, htmlspecialchars($value));
        }
    }

    return $xml->asXML();
}

/**
 * Recursive helper to add array data to XML
 */
function addArrayToXml(\SimpleXMLElement $xml, array $data): void
{
    foreach ($data as $key => $value) {
        if (is_array($value)) {
            if (is_numeric($key)) {
                // Handle numeric keys (like arrays of options)
                $childNode = $xml->addChild('option');
            } else {
                $childNode = $xml->addChild($key);
            }
            addArrayToXml($childNode, $value);
        } else {
            if (is_numeric($key)) {
                $xml->addChild('item', htmlspecialchars($value));
            } else {
                $xml->addChild($key, htmlspecialchars($value));
            }
        }
    }
}
