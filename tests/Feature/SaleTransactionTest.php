<?php

use App\Enums\PartyTypeEnums;
use App\Enums\ProductStockStatusEnums;
use App\Enums\StockTypeEnums;
use App\Enums\TransactionAccountTypesEnum;
use App\Enums\TransactionTypeEnums;
use App\Models\Company;
use App\Models\CompanyBranch;
use App\Models\CompanyParty;
use App\Models\Order;
use App\Models\OrderItems;
use App\Models\PartyType;
use App\Models\PaymentMode;
use App\Models\Product;
use App\Models\ProductDetail;
use App\Models\ProductStockHistory;
use App\Models\Tax;
use App\Models\User;
use Laravel\Sanctum\Sanctum;
use App\Models\Role;
use App\Models\RolePermission;
use Illuminate\Support\Facades\Http;

beforeEach(function () {

    $this->user = User::factory()->create([
        'whiteLabel' => 'admin',
    ]);

    $this->company = Company::factory()->create(["user_id" =>  $this->user->id, "isEBM" => true]);
    $this->branch = CompanyBranch::factory()->create(
        [
            "company_id" => $this->company->id,
            'isEBM' => true,
            "mode" => 'N',
            'cluster' => 'https://server.zata.rw',
            'clusterName' => 'zata',
            'isInitialised' => true
        ]
    );

    $this->permission = RolePermission::factory()->create();
    $this->role = Role::factory()->create([
        'user_id' => $this->user->id,
        'company_id' => $this->company->id,
        'role_permission_id' => $this->permission
    ]);

    $this->apiUrl = '/api/v1/transaction/sale';
    $this->mainUrl = '/api/v1/transaction';
    $this->adminUrl = '/admin';

    $tax = [['code' => 'A', 'rate' => 0], ['code' => 'B', 'rate' => 18], ['code' => 'C', 'rate' => 0], ['code' => 'D', 'rate' => 0]];

    foreach ($tax as $item) {
        Tax::factory()->create([
            'code' => $item['code'],
            'rate' => $item['rate']
        ]);
    }

    $this->saveStockItems = $this->branch->cluster . '/' . $this->branch->clusterName .  config('ebm.save_stock_items');
    $this->saveStockMaster = $this->branch->cluster . '/' . $this->branch->clusterName . config('ebm.save_stock_master');
    $this->saveSaleTransactionUrl = $this->branch->cluster . '/' . $this->branch->clusterName .  config('ebm.sale_transaction');

    Sanctum::actingAs($this->user);
});

it("can create sale transaction", function (array $payload) {

    $responseData = [
        "resultCd" => "000",
        "resultMsg" => "It is succeeded",
        "resultDt" => "20240707160133",
        "data" => [
            "rcptNo" => fake()->randomNumber(),
            "intrlData" => "UPL2VNF4H6ELEFGNW5JZGK4F4M",
            "rcptSign" => "KDLJFNJGIY4SSDVC",
            "totRcptNo" => fake()->randomNumber(),
            "vsdcRcptPbctDate" => "20240707160133",
            "sdcId" => "erererer",
            "mrcNo" => "erbiver"
        ]
    ];

    $stockResponse = [
        "resultCd" => "000",
        "resultMsg" => "It is succeeded",
        "resultDt" => "20200226195801",
        "data" => null
    ];

    $taxblAmtA = 0;
    $taxblAmtB = 0;
    $taxAmtB = 0;
    $taxblAmtC = 0;
    $taxblAmtD = 0;
    $totAmt = 0;

    $items = $payload['items'];
    $itemsIDs = array_unique(array_column($items, 'productID'));

    $productsWithTaxInfo = Product::whereIn('id', $itemsIDs)
        ->where('branch_id', $this->branch->id)
        ->with([
            'productTax:id,code,rate',
            'productPackingUnit:id,code',
            'productQuantityUnit:id,code',
            'productClass:id,code',
            'productDetails:id,product_id,expireDate,batchNumber'
        ])
        ->get();

    foreach ($items as $item) {
        $productDetails = $productsWithTaxInfo->where("id", $item['productID'])->first();
        $itemAmount = $item['units'] * $item['unitPrice'];
        $discountAmount = $itemAmount * ($item['discountRate'] / 100);
        $taxableAmount = $itemAmount - $discountAmount;

        $taxAmount = $productDetails->productTax->code === 'B' ? round($taxableAmount * ($productDetails->productTax->rate / 118), 2) : 0;

        switch ($productDetails->productTax->code) {
            case "A":
                $taxblAmtA += $taxableAmount;
                break;
            case "B":
                $taxblAmtB += $taxableAmount;
                $taxAmtB += $taxAmount;
                break;
            case "C":
                $taxblAmtC += $taxableAmount;
                break;
            case "D":
                $taxblAmtD += $taxableAmount;
                break;
        }

        $totAmt += $itemAmount;
    }

    Http::fake([
        $this->saveSaleTransactionUrl => Http::response($responseData),
        $this->saveStockItems => Http::response($stockResponse),
        $this->saveStockMaster => Http::response($stockResponse)
    ]);

    $this->withHeaders([
        'companyId' => $this->company->id,
        'branchId' => $this->branch->id
    ]);

    $response = $this->postJson($this->apiUrl, $payload);

    $response->assertStatus(200)
        ->assertJson(['message' => 'Sale transaction created successfully'])
        ->assertExactJsonStructure(['message', 'invoiceID']);

    $invoiceId = $response->json('invoiceID');

    $this->assertDatabaseHas('Orders', [
        'id' => $invoiceId,
        'branch_id' => $this->branch->id,
        'company_party_id' => $payload['customerID'] ?? null,
        'transactionType' => TransactionTypeEnums::SALES->value,
        'user_id' => $this->user->id,
        'type' => 'NS'
    ]);

    foreach ($payload['items'] as $item) {
        $product = Product::find($item['productID']);
        $productDetail = $product->productDetails()->where('batchNumber', $item['batchNumber'])->first();
        $expectedStock = 100 - $item['units'];
        $this->assertEquals($expectedStock, $productDetail->currentStock);

        expect($productDetail->status)->toBe(ProductStockStatusEnums::IN_STOCK->value);
        expect($productDetail->currentStock)->toBe($expectedStock);
        expect($productDetail->batchNumber)->toBe($item['batchNumber']);

        $productStockHistory = ProductStockHistory::where([
            'product_id' => $item['productID'],
            'batchNumber' => $item['batchNumber'],
            'orderType' => TransactionTypeEnums::SALES->value,
            'stockType' => StockTypeEnums::OUT->value,
        ])->first();

        expect((float)$productStockHistory->quantity)->toBe((float)$item['units']);
        expect($productStockHistory->batchNumber)->toBe($item['batchNumber']);
    }

    $recorded = Http::recorded();

    expect($recorded->count())->toBe(7);

    $salesRequest = $recorded->get(0);
    expect($salesRequest[0]->url())->toBe($this->saveSaleTransactionUrl);
    expect($salesRequest[0]->method())->toBe('POST');

    $salesData = $salesRequest[0]->data();

    expect($salesData)->toHaveKeys([
        'tin',
        'bhfId',
        'invcNo',
        'custNm',
        'custTin',
        'prcOrdCd',
        'salesTyCd',
        'rcptTyCd',
        'pmtTyCd',
        'totItemCnt',
        'itemList'
    ]);

    expect($salesData['tin'])->toBe((string)$this->company->tin);
    expect($salesData['bhfId'])->toBe($this->branch->branchCode);
    expect($salesData['totItemCnt'])->toBe(count($payload['items']));
    expect($salesData['salesTyCd'])->toBe('N');
    expect($salesData['rcptTyCd'])->toBe('S');

    expect((float)$salesData['taxblAmtA'])->toBe((float)$taxblAmtA);
    expect((float)$salesData['taxblAmtB'])->toBe((float)$taxblAmtB);
    expect((float)$salesData['taxblAmtC'])->toBe((float)$taxblAmtC);
    expect((float)$salesData['taxblAmtD'])->toBe((float)$taxblAmtD);
    expect((float)$salesData['taxAmtB'])->toBe((float)$taxAmtB);
    expect((float)$salesData['totAmt'])->toBe((float)$totAmt);

    expect(count($salesData['itemList']))->toBe(count($payload['items']));

    foreach ($salesData['itemList'] as $index => $item) {
        expect($item)->toHaveKeys([
            'itemSeq',
            'itemCd',
            'itemClsCd',
            'itemNm',
            'pkgUnitCd',
            'qtyUnitCd',
            'qty',
            'prc',
            'splyAmt',
            'taxblAmt',
            'taxTyCd',
            'totAmt'
        ]);

        expect($item['itemSeq'])->toBe($index + 1);
        expect((float)$item['qty'])->toBeGreaterThan(0);
        expect((float)$item['prc'])->toBeGreaterThan(0);
    }

    expect($salesData['receipt'])->toHaveKeys([
        'rptNo',
        'trdeNm',
        'adrs',
        'topMsg',
        'btmMsg',
        'prchrAcptcYn',
        'custMblNo'
    ]);

    $stockMasterRequests = $recorded->slice(1, 5);
    foreach ($stockMasterRequests as $index => $stockRequest) {
        expect($stockRequest[0]->url())->toBe($this->saveStockMaster);
        expect($stockRequest[0]->method())->toBe('POST');

        $stockData = $stockRequest[0]->data();
        expect($stockData)->toHaveKeys([
            'tin',
            'bhfId',
            'itemCd',
            'rsdQty',
            'regrId',
            'regrNm',
            'modrNm',
            'modrId'
        ]);

        expect($stockData['tin'])->toBe((string)$this->company->tin);
        expect($stockData['bhfId'])->toBe($this->branch->branchCode);
        expect($stockData['regrId'])->toBe('HiqAfrica');
        expect($stockData['regrNm'])->toBe('HiqAfrica');
        expect((int)$stockData['rsdQty'])->toBeGreaterThan(0);
    }

    $stockItemsRequest = $recorded->get(6);
    expect($stockItemsRequest[0]->url())->toBe($this->saveStockItems);
    expect($stockItemsRequest[0]->method())->toBe('POST');

    $stockItemsData = $stockItemsRequest[0]->data();
    expect($stockItemsData)->toHaveKeys([
        'tin',
        'bhfId',
        'sarNo',
        'regTyCd',
        'sarTyCd',
        'ocrnDt',
        'totItemCnt',
        'totTaxblAmt',
        'totTaxAmt',
        'totAmt',
        'itemList'
    ]);

    expect($stockItemsData['tin'])->toBe((string)$this->company->tin);
    expect($stockItemsData['bhfId'])->toBe($this->branch->branchCode);
    expect($stockItemsData['regTyCd'])->toBe('M');
    expect($stockItemsData['sarTyCd'])->toBe('11');
    expect($stockItemsData['totItemCnt'])->toEqual(count($payload['items']));
    expect($stockItemsData['totTaxblAmt'])->toEqual($totAmt);
    expect($stockItemsData['totAmt'])->toEqual($totAmt);

    expect(count($stockItemsData['itemList']))->toBe(count($payload['items']));

    foreach ($stockItemsData['itemList'] as $index => $stockItem) {
        expect($stockItem)->toHaveKeys([
            'itemSeq',
            'itemCd',
            'itemClsCd',
            'itemNm',
            'pkgUnitCd',
            'qtyUnitCd',
            'qty',
            'prc',
            'splyAmt',
            'taxblAmt',
            'taxTyCd',
            'totAmt'
        ]);

        expect($stockItem['itemSeq'])->toBe($index + 1);
        expect($stockItem['qtyUnitCd'])->toBe('U');
        expect((int)$stockItem['qty'])->toBeGreaterThan(0);
    }

    foreach ($recorded as $request) {
        expect($request[1]->status())->toBe(200);
        expect($request[1]->header('Content-Type'))->toContain('application/json');
    }

    // assert TransactionAccountData is  also created 
    $this->assertDatabaseHas('TransactionAccount', [
        'branch_id' => $this->branch->id,
        'payment_mode_id' => $payload['paymentMethodID'],
        'user_id' => $this->user->id,
        'amount' => $totAmt,
        'type' => TransactionAccountTypesEnum::CREDIT->value,
        'sourceType' => TransactionTypeEnums::SALES->value,
        'source_id' => $invoiceId,
        'description' => $payload['note'],
    ]);

    // assert OrderReceiptSignature is also created 
    $this->assertDatabaseHas('OrderReceiptSignature', [
        'order_id' => $invoiceId,
        'receiptNumber' => $responseData['data']['rcptNo'],
        'internalData' => $responseData['data']['intrlData'],
        'receiptSignature' => $responseData['data']['rcptSign'],
        'totalReceiptNumber' => $responseData['data']['totRcptNo'],
        'vsdcReceiptPublishDate' => strtotime($responseData['data']['vsdcRcptPbctDate']),
        'sdcId' => $responseData['data']['sdcId'],
        'mrcNumber' => $responseData['data']['mrcNo'],
    ]);

    // asert logs are also created 
    $this->assertDatabaseHas('LogTrack', [
        'company_id' => $this->company->id,
        'branch_id' => $this->branch->id,
        'user_id' => $this->user->id,
        'message' => "Transaction Created, ID: $invoiceId",
        'action' => 'create',
    ]);
})->with([
    'sale with multiple items' => function () {
        $product1 = Product::factory()->create([
            'branch_id' => $this->branch->id,
            'tax_id' => Tax::where('code', 'B')->first()->id
        ]);
        $product2 = Product::factory()->create([
            'branch_id' => $this->branch->id,
            'tax_id' => Tax::where('code', 'A')->first()->id
        ]);
        $product3 = Product::factory()->create([
            'branch_id' => $this->branch->id,
            'tax_id' => Tax::where('code', 'C')->first()->id
        ]);
        $product4 = Product::factory()->create([
            'branch_id' => $this->branch->id,
            'tax_id' => Tax::where('code', 'D')->first()->id
        ]);

        $items = [];

        foreach ([$product1, $product2, $product3, $product4] as $product) {
            ProductDetail::factory()->create([
                'product_id' => $product->id,
                'currentStock' => 100,
                'batchNumber' => 'BATCH00' . $product->id,
                'discountRate' => 0
            ]);

            $items[] = [
                'productID' => $product->id,
                'units' => rand(5, 30),
                'unitPrice' => rand(1000, 5000),
                'discountRate' => 0,
                'batchNumber' => 'BATCH00' . $product->id,
                'movingUnit' => 'sub'
            ];
        }

        $productDetailExt = ProductDetail::factory()->create([
            'product_id' => $product4->id,
            'currentStock' => 100,
            'batchNumber' => 'uuuKKty',
            'discountRate' => 0
        ]);

        $items[] = [
            'productID' => $product4->id,
            'units' => rand(5, 30),
            'unitPrice' => rand(1000, 5000),
            'discountRate' => 0,
            'batchNumber' => $productDetailExt->batchNumber,
            'movingUnit' => 'sub'
        ];

        $companyParty = CompanyParty::factory()->create([
            'company_id' => $this->company->id,
            'party_type_id' => PartyType::where('name', PartyTypeEnums::CUSTOMER->value)->first()->id,
        ]);

        return [
            'customerID' => $companyParty->id,
            'purchaseCode' => 'PUR001',
            'paymentMethodID' => PaymentMode::factory()->create(['forEbm' => true])->id,
            'transactionDate' => now()->format('Y-m-d'),
            'note' => 'Multiple items sale',
            'items' => $items
        ];
    },
]);

it("can throw errors during create sale transaction ", function (array $payload) {

    $this->withHeaders([
        'companyId' => $this->company->id,
        'branchId' => $this->branch->id
    ]);

    $ebmData = $payload['ebm'];
    $response = $this->postJson($this->apiUrl, $ebmData);

    $response->assertStatus($payload['code'])
        ->assertJson([
            'message' => $payload['message'],
        ]);
})->with([
    function () {

        $product = Product::factory()->create([
            'branch_id' => $this->branch->id,
        ]);

        $stock =    ProductDetail::factory()->create([
            'product_id' => $product->id,
            'currentStock' => 100,
        ]);

        return [
            'ebm' => [
                'purchaseCode' => '*********',
                'paymentMethodID' => 90000,
                'transactionDate' => now()->format('Y-m-d'),
                'note' => 'Note',
                'items' => [
                    [
                        'productID' => $product->id,
                        'units' => 10,
                        'unitPrice' => 1000,
                        'discountRate' => 10,
                        'batchNumber' => $stock->batchNumber,
                        'movingUnit' => 'sub'
                    ]
                ]
            ],
            'message' => 'Payment method not found',
            'code' => 404
        ];
    },

    function () {

        $product = Product::factory()->create([
            'branch_id' => $this->branch->id,
        ]);

        ProductDetail::factory()->create([
            'product_id' => $product->id,
            'currentStock' => 100,
        ]);

        return [
            'ebm' => [
                'purchaseCode' => '*********',
                'paymentMethodID' => PaymentMode::factory()->create([
                    'forEbm' => true
                ])->id,
                'transactionDate' => now()->format('Y-m-d'),
                'note' => 'Note',
                'items' => [
                    [
                        'productID' => 1212,
                        'units' => 10,
                        'unitPrice' => 1000,
                        'discountRate' => 10,
                        'batchNumber' => '*********',
                        'movingUnit' => 'sub'
                    ]
                ]
            ],
            'message' => 'Product not found',
            'code' => 404
        ];
    },
    function () {

        $product = Product::factory()->create([
            'branch_id' => $this->branch->id,
        ]);

        ProductDetail::factory()->create([
            'product_id' => $product->id,
            'currentStock' => 100,
        ]);

        return [
            'ebm' => [
                'purchaseCode' => '*********',
                'paymentMethodID' => PaymentMode::factory()->create([
                    'forEbm' => true
                ])->id,
                'transactionDate' => now()->format('Y-m-d'),
                'note' => 'Note',
                'items' => [
                    [
                        'productID' => $product->id,
                        'units' => 10,
                        'unitPrice' => 1000,
                        'discountRate' => 10,
                        'batchNumber' => '*********',
                        'movingUnit' => 'sub'
                    ]
                ]
            ],
            'message' => 'Batch number not found',
            'code' => 400
        ];
    },

    function () {

        $product = Product::factory()->create([
            'branch_id' => $this->branch->id,
            'description_three' => 'yes'
        ]);

        ProductDetail::factory()->create([
            'product_id' => $product->id,
            'currentStock' => 100,
            'batchNumber' => '*********',
        ]);

        return [
            'ebm' => [
                'purchaseCode' => '*********',
                'paymentMethodID' => PaymentMode::factory()->create([
                    'forEbm' => true
                ])->id,
                'transactionDate' => now()->format('Y-m-d'),
                'note' => 'Note',
                'items' => [
                    [
                        'productID' => $product->id,
                        'units' => 10000,
                        'unitPrice' => 1000,
                        'discountRate' => 10,
                        'batchNumber' => '*********',
                        'movingUnit' => 'sub'
                    ]
                ]
            ],
            'message' => 'Insufficient stock quantity',
            'code' => 400
        ];
    },

]);

it("can create tax calculation", function (array $payload) {

    $this->withHeaders([
        'companyId' => $this->company->id,
        'branchId' => $this->branch->id
    ]);

    $response = $this->postJson('api/v1/transaction/calculate', $payload);

    $response->assertStatus(200)
        ->assertExactJsonStructure([
            '*' => [
                'id',
                'code',
                'name',
                'class',
                'units',
                'unitPrice',
                'batchNumber',
                'movingUnit',
                'packagingUnit',
                'quantityUnit',
                'discountAmount',
                'discountRate',
                'taxAmount',
                'taxRate',
                'taxableAmount',
                'taxName',
                'expireDate'
            ]
        ]);
})->with([
    function () {

        $product = Product::factory()->create([
            'branch_id' => $this->branch->id,
        ]);

        ProductDetail::factory()->create([
            'product_id' => $product->id,
            'currentStock' => 100,
            'batchNumber' => '*********',
        ]);

        return [
            'items' => [
                [
                    'productID' => $product->id,
                    'units' => 10,
                    'unitPrice' => 1000,
                    'discountRate' => 10,
                    'batchNumber' => '*********',
                ]
            ]

        ];
    }
]);

it('can get invoice by invoicenumber', function (array $payload) {

    $this->withHeaders([
        'companyId' => $this->company->id,
        'branchId' => $this->branch->id
    ]);

    $response = $this->get("api/v1/transaction/{$payload['invoiceNumber']}");

    $response->assertStatus(200)
        ->assertExactJsonStructure([
            'id',
            'invoiceNumber',
            'originalInvoiceNumber',
            'clientTin',
            'clientName',
            'clientPhoneNumber',
            'salesTypeCode',
            'receiptTypeCode',
            'paymentTypeCode',
            'salesStatusCode',
            'confirmationDate',
            'type',
            'isRefunded',
            'taxblAmtA',
            'taxblAmtB',
            'taxblAmtC',
            'taxblAmtD',
            'taxAmtA',
            'taxAmtB',
            'taxAmtC',
            'taxAmtD',
            'totTaxblAmt',
            'totTaxAmt',
            'totAmt',
            'salesDate',
            'note',
            'items' => [
                '*' => [
                    'productName',
                    'units',
                    'unitPrice',
                    'taxAmount',
                    'taxRate',
                    'taxName',
                    'totalAmount',
                    'totalDiscount',
                    'discountRate',
                ]
            ]
        ]);
})->with([
    function () {

        $product = Product::factory()->create([
            'branch_id' => $this->branch->id,
        ]);

        ProductDetail::factory()->create([
            'product_id' => $product->id,
            'currentStock' => 100,
        ]);

        $order = Order::factory()->create([
            'branch_id' => $this->branch->id,
            'isRefunded' => false,
            'company_party_id' => CompanyParty::factory()->create()->id,
        ]);

        OrderItems::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product->id,
        ]);

        return [
            'invoiceNumber' => $order->id
        ];
    }
]);

it('can get all invoices', function (array $payload) {

    $this->withHeaders([
        'companyId' => $this->company->id,
        'branchId' => $this->branch->id
    ]);

    $response = $this->get(
        "{$this->mainUrl}?invoiceType={$payload['invoiceType']}&perPage={$payload['perPage']}&page={$payload['page']}",
    );

    $response->assertStatus(200)
        ->assertExactJsonStructure([
            'data' => [
                '*' => [
                    'id',
                    'invoiceNumber',
                    'clientName',
                    'salesDate',
                    'totalAmount',
                    'synced',
                    'isRefunded',
                    'status',
                    'itemsCount',
                    'invoiceType',
                ]
            ],
            'currentPage',
            'lastPage',
            'itemsPerPage',
            'pageItems',
            'total',
        ]);

    expect($response->json('data'))->toHaveCount(2);
    expect($response->json('currentPage'))->toEqual($payload['page']);
    expect($response->json('lastPage'))->toEqual(1);
    expect($response->json('itemsPerPage'))->toEqual($payload['perPage']);
    expect($response->json('pageItems'))->toEqual(2);
    expect($response->json('total'))->toEqual(2);
})->with([

    function () {
        $order1 =   Order::factory()->create([
            'branch_id' => $this->branch->id,
            'isRefunded' => false,
            'company_party_id' => CompanyParty::factory()->create()->id,
            'transactionType' => TransactionTypeEnums::SALES->value
        ]);

        $order2 =   Order::factory()->create([
            'branch_id' => $this->branch->id,
            'isRefunded' => false,
            'company_party_id' => CompanyParty::factory()->create()->id,
            'transactionType' => TransactionTypeEnums::SALES->value
        ]);

        $order3 =   Order::factory()->create([
            'branch_id' => $this->branch->id,
            'isRefunded' => false,
            'company_party_id' => CompanyParty::factory()->create()->id,
            'transactionType' =>   TransactionTypeEnums::PROFORMA->value,
        ]);

        OrderItems::factory()->create([
            'order_id' => $order1->id,
            'product_id' => Product::factory()->create([
                'branch_id' => $this->branch->id,
            ])->id,
        ]);

        OrderItems::factory()->count(4)->create([
            'order_id' => $order2->id,
            'product_id' => Product::factory()->create([
                'branch_id' => $this->branch->id,
            ])->id,
        ]);

        OrderItems::factory()->count(4)->create([
            'order_id' => $order3->id,
            'product_id' => Product::factory()->create([
                'branch_id' => $this->branch->id,
            ])->id,
        ]);

        return [
            'invoiceType' => TransactionTypeEnums::SALES->value,
            'perPage' => 10,
            'page' => 1
        ];
    }
]);

it("can generate PDF url for transaction ", function (array $payload) {

    $this->withHeaders([
        'companyId' => $this->company->id,
        'branchId' => $this->branch->id
    ]);

    $response = $this->get("$this->mainUrl/download-signature/{$payload['invoiceNumber']}");

    $response->assertStatus(200)->assertJsonStructure(['url', 'expires_at']);

    $pdf = $response->json('url');
    $streamedPdf =   $this->get($pdf);

    $streamedPdf->assertHeader('Content-Type', 'application/pdf');
})->with([
    function () {
        $order = Order::factory()->create([
            'branch_id' => $this->branch->id,
            'isRefunded' => false,
            'company_party_id' => CompanyParty::factory()->create()->id,
            'transactionType' => TransactionTypeEnums::SALES->value
        ]);

        return [
            'invoiceNumber' => $order->id,
        ];
    }
]);

it('can admin search invoice', function (array $payload) {

    $this->withHeaders([
        'companyId' => $this->company->id,
        'branchId' => $this->branch->id
    ]);

    $response = $this->get(
        "{$this->adminUrl}/search-json?invoiceType={$payload['invoiceType']}&startDate={$payload['startDate']}&endDate={$payload['endDate']}",
    );

    $response->assertStatus(200)
        ->assertJsonStructure([
            'data' => [
                '*' => [
                    'id',
                    'invoiceNumber',
                    'clientName',
                    'salesDate',
                ]
            ],
            'currentPage',
            'lastPage',
            'itemsPerPage',
            'pageItems',
            'total',
        ]);
})->with([
    function () {

        $order1 =   Order::factory()->create([
            'branch_id' => $this->branch->id,
            'isRefunded' => false,
            'company_party_id' => CompanyParty::factory()->create()->id,
            'transactionType' => TransactionTypeEnums::SALES->value
        ]);

        $order2 =   Order::factory()->create([
            'branch_id' => $this->branch->id,
            'isRefunded' => false,
            'company_party_id' => CompanyParty::factory()->create()->id,
            'transactionType' => TransactionTypeEnums::SALES->value
        ]);

        $order3 =   Order::factory()->create([
            'branch_id' => $this->branch->id,
            'isRefunded' => false,
            'company_party_id' => CompanyParty::factory()->create()->id,
            'transactionType' =>   TransactionTypeEnums::PROFORMA->value,
        ]);

        OrderItems::factory()->create([
            'order_id' => $order1->id,
            'product_id' => Product::factory()->create([
                'branch_id' => $this->branch->id,
            ])->id,
        ]);

        OrderItems::factory()->count(4)->create([
            'order_id' => $order2->id,
            'product_id' => Product::factory()->create([
                'branch_id' => $this->branch->id,
            ])->id,
        ]);

        OrderItems::factory()->count(4)->create([
            'order_id' => $order3->id,
            'product_id' => Product::factory()->create([
                'branch_id' => $this->branch->id,
            ])->id,
        ]);

        $searchQuery = 'invoice';

        return [
            'perPage' => 10,
            'page' => 1,
            'searchQuery' => $searchQuery,
            'invoiceType' => TransactionTypeEnums::SALES->value,
            'startDate' => '2022-01-01',
            'endDate' => '2025-12-31',
        ];
    }
]);
