1. UI Theory 
2. subscription
3. transaction api - redefine it to prevent ( timeout, dependecy unvailable, testing ) done
4. subscription api hardening 
5. delete option [ delete products, delete branches]
6. DB Backup
7. Single sign in with URL 
8. hardening test 
9. onBoarding 
10. admin dashbaord search [ product , branches , company, transaction  and users ]
11. accurate dashbaird analytics 
12. service dependency correction [ like make sure all service are not writting validation second times like creating global validatin for product validation ]
13. cashing using redis [ dashbaord and some listing like prudtc or logs ]
14. Pharmacy services validaion and testing 
15. invoice testing 
16. API Testing 
17. security enhance ment [ admin passwords , api rate limiting and payload return validation ]