<?php

namespace App\Jobs;

use App\Services\StockTrackingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class StockIoSave implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */

    protected $stockService;
    protected $data;
    protected $branch;
    public function __construct($data, $branch)
    {
        $this->data = $data;
        $this->branch = $branch;
        $this->stockService = app(StockTrackingService::class);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        //
         $this->stockService->stockIoSave($this->data, $this->branch);
    }
}
