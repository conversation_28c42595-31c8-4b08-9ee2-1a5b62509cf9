<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

use App\Services\TransactionAccountService;
class TransactionAccountData implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $transactionAccountService;
    protected $data;
    protected $branch;

    public function __construct($data)
    {
        //
        $this->data = $data;
        $this->transactionAccountService = app(TransactionAccountService::class);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        //
        $this->transactionAccountService->createTransaction($this->data);
    }
}
