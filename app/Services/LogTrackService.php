<?php

namespace App\Services;

use App\Data\LogTrackData;
use App\Models\LogTrack;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Pagination\LengthAwarePaginator;

class LogTrackService
{

    // TODO, log tyrack based on user role 
    public function createLog(LogTrackData $data): void
    {

        LogTrack::create([
            'company_id' => $data->company_id,
            'branch_id' => $data->branch_id,
            'user_id' => $data->user_id,
            'message' => $data->message,
            'action' => $data->action,
        ]);
    }

    public function getLogs(
        int $companyId,
        $branchId,
        int $userId,
        int $page,
        int $perPage,
        ?string $fromDate,
        ?string $toDate
    ): LengthAwarePaginator {

        return LogTrack::where('company_id', $companyId)
            ->with(['user:id,name', 'branch:id,name', 'company:id,name'])
            ->orWhere('branch_id', $branchId)
            ->orWhere('user_id', $userId)
            ->FilterByDate($fromDate, $toDate)
            ->latest()
            ->paginate(perPage: $perPage, page: $page)
            ->through(fn($log) => [
                'user' => $log->user->name,
                'branch' => $log->branch->name,
                'message' => $log->message,
                'action' => $log->action,
                'dateCreate' => $log->created_at
            ]);
    }
}
