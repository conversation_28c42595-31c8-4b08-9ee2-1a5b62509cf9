<?php

namespace App\Services;

use App\Data\LogTrackData;
use App\Data\PurchaseData;
use App\Data\StockMasterData;
use App\Data\TransactionAccountData;
use App\Jobs\TransactionAccountData as TransactionAccountDataService;
use App\Data\UpdateProductQuantityData;
use App\Enums\TransactionTypeEnums;
use App\Exceptions\NotFound;
use App\Models\Company;
use App\Models\CompanyBranch;
use App\Models\Order;
use App\Models\OrderItems;
use App\Models\User;
use App\Models\PaymentMode;
use App\Models\Tax;
use Carbon\Carbon;
use Illuminate\Pagination\LengthAwarePaginator;
use App\Data\TransactionPosData;
use App\Data\UpdateProductQuantityOutData;
use App\Enums\LogTrackStatusEnums;
use App\Enums\ProductMovingUnitEnums;
use App\Enums\TransactionAccountTypesEnum;
use App\Exceptions\BadRequest;
use App\Exceptions\ServerError;
use App\Jobs\StockIoSave;
use App\Jobs\StockMasterSave;
use App\Models\CompanyParty;
use App\Jobs\OrderReceiptSignature as OrderReceiptSignatureJob;
use App\Models\OrderReceiptSignature;
use App\Models\PartyType;
use App\Models\Product;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TransactionService
{

    public function __construct(
        private ProductService $productService,
        protected TransactionAccountService $transactionAccountService,
        protected EbmService $ebmService,
        protected CompanyPartyService $companyPartyService,
        protected LogTrackService $logService,
        protected StockTrackingService $stockTrackingService
    ) {}

    public function showTransaction(int $id, CompanyBranch $branch): array
    {

        $transaction = $this->getTransactionById($id, $branch->id);
        return $transaction->toArray();
    }

    public function createPurchase(PurchaseData $data, CompanyBranch $branch, Company $company, User $user)
    {

        $customer = CompanyParty::where('id', $data->customerID)
            ->where('company_id', $company->id)
            ->first();

        throw_if(is_null($customer), NotFound::class, 'Customer not found');

        foreach ($data->items as $item) {
            $product = Product::where('id', $item->productID)->where('branch_id', $branch->id)->exists();
            throw_if(!$product, NotFound::class, 'Product not found');
        }

        $transaction = Order::create([
            'branch_id' => $branch->id,
            'user_id' => $user->id,
            'company_party_id' => $data->customerID,
            'transaction_date' => $data->transactionDate,
            'note' => $data->note,
            'transactionType' => TransactionTypeEnums::PURCHASE->value,
            'type' => 'PO',
            'clientName' => $customer->name,
            'salesDate' => strtotime($data->transactionDate),
        ]);

        foreach ($data->items as $item) {

            $product = Product::where('id', $item->productID)->first();
            OrderItems::create([
                'order_id' => $transaction->id,
                'product_id' => $item->productID,
                'quantity' => $item->units,
                'price' => 0,
                'discount' => 0,
                'productName' => $product->name,
                'productCode' => $product->itemCode
            ]);
        }
    }

    public function storeSale(TransactionPosData  $data, CompanyBranch $branch, Company $company, User $user): int
    {

        $receiptTypeCode = 'S';
        $salesTypeCode =  $branch->mode;
        $isEBM = $branch->isEBM;

        $this->productService->validateProductStockQuantity($data->items);
        $customer = $this->validateCustomer($data, $company);

        switch ($isEBM) {
            case true:
                $invoiceData = $this->buildEbmInvoice($data, $customer, $branch, $company, $receiptTypeCode, $salesTypeCode);
                $invoice =  $this->saveEbmTransaction($invoiceData, $branch, $user, $customer, TransactionTypeEnums::SALES->value);
                break;
            case false:
                $invoice =   $this->saveTransaction($data, $branch, $user, $receiptTypeCode, $salesTypeCode, $customer, TransactionTypeEnums::SALES->value);
                break;
        }

        return $invoice;
    }

    public function storeProforma($data, CompanyBranch $branch, Company $company, User $user): int
    {
        $receiptTypeCode = 'S';
        $salesTypeCode =  'P';
        $isEBM = $branch->isEBM;

        $this->productService->validateProductStockQuantity($data->items);
        $customer = $this->validateCustomer($data, $company);

        switch ($isEBM) {
            case true:
                $invoiceData = $this->buildEbmInvoice($data, $customer, $branch, $company, $receiptTypeCode, $salesTypeCode);
                $invoice =  $this->saveEbmTransaction($invoiceData, $branch, $user, $customer, TransactionTypeEnums::PROFORMA->value);
                break;
            case false:
                $invoice =   $this->saveTransaction($data, $branch, $user, $receiptTypeCode, $salesTypeCode, $customer, TransactionTypeEnums::PROFORMA->value);
                break;
        }

        return $invoice;
    }

    public function convertProformaToSale(int $id, $data, CompanyBranch $branch, Company $company, User $user): int
    {

        $receiptTypeCode = 'S';
        $isEBM = $branch->isEBM;

        $transaction = $this->getTransactionById($id, $branch->id);
        $salesTypeCode =  $branch->mode;

        throw_if($transaction->isRefunded, NotFound::class, 'Transaction already converted');

        $saleData = TransactionPosData::from([
            'insuranceTin' => $transaction->insuranceTin,
            'prescriptionNumber' => $transaction->prescriptionNumber,
            'paymentMethodID' => PaymentMode::where('code', $transaction->paymentTypeCode)->first()->id,
            'customerID' => $transaction->company_party_id,
            'transactionDate' => $data->transactionDate,
            'purchaseCode' => $data->purchaseCode,
            'note' => $data->note,
            'items' => $transaction->items->map(fn($item) => [
                'productID' => $item->product_id,
                'units' => $item->quantity,
                'unitPrice' => $item->price,
                'discountRate' => $item->discount,
                'batchNumber' => $item->batchNumber,
                'expireDate' => $item->expireDate,
            ])
        ]);

        $customer = $this->validateCustomer($saleData,  $company);
        switch ($isEBM) {
            case true:
                $invoiceData = $this->buildEbmInvoice($saleData, $customer, $branch, $company, $receiptTypeCode, $salesTypeCode);
                $invoice =  $this->saveEbmTransaction($invoiceData, $branch, $user, $customer, TransactionTypeEnums::SALES->value);
                break;
            case false:
                $invoice =   $this->saveTransaction($saleData, $branch, $user, $receiptTypeCode, $salesTypeCode,  $customer, TransactionTypeEnums::SALES->value);
                break;
        }
        $transaction->update(['isRefunded' => true]);

        return $invoice;
    }

    public function storeRefund(int $id, $data, CompanyBranch $branch, Company $company, User $user): int
    {

        $transaction = $this->getTransactionById($id, $branch->id);

        throw_if($transaction->isRefunded, NotFound::class, 'Transaction already refunded');

        $receiptTypeCode = 'R';
        $salesTypeCode =  $transaction->salesTypeCode;

        $refundData = TransactionPosData::from([
            'insuranceTin' => $transaction->insuranceTin,
            'prescriptionNumber' => $transaction->prescriptionNumber,
            'paymentMethodID' => PaymentMode::where('code', $transaction->paymentTypeCode)->first()->id,
            'customerID' => $transaction->company_party_id,
            'transactionDate' => $data->transactionDate,
            'purchaseCode' => $data->purchaseCode,
            'note' => $data->note,
            'items' => $transaction->items->map(fn($item) => [
                'productID' => $item->product_id,
                'units' => $item->quantity,
                'unitPrice' => $item->price,
                'discountRate' => $item->discount,
                'batchNumber' => $item->batchNumber,
                'expireDate' => $item->expireDate,
            ])
        ]);

        $customer = $this->validateCustomer($refundData, $company);
        switch ($branch->isEBM) {
            case true:
                $invoiceData = $this->buildEbmInvoice($refundData, $customer, $branch, $company, $receiptTypeCode, $salesTypeCode, $transaction->invoiceNumber);
                $invoice =   $this->saveEbmTransaction($invoiceData, $branch, $user, $customer, TransactionTypeEnums::SALES_RETURN->value);
                break;
            case false:

                $invoice =    $this->saveTransaction($refundData, $branch, $user, $receiptTypeCode, $salesTypeCode,  $customer, TransactionTypeEnums::SALES_RETURN->value, $transaction->invoiceNumber);
                break;
        }

        $transaction->update(['isRefunded' => true]);

        return $invoice;
    }

    public function storeManualRefund(TransactionPosData $data, CompanyBranch $branch, Company $company, User $user)
    {

        if (is_null($data->originalInvoiceNumber)) {
            throw  new BadRequest('Original invoice number is required');
        }

        $checkRefundStatus = Order::where('branch_id', $branch->id)
            ->where('originalInvoiceNumber', $data->originalInvoiceNumber)
            ->where('transactionType', TransactionTypeEnums::SALES_RETURN->value)
            ->first();

        if (!is_null($checkRefundStatus)) {
            throw  new BadRequest('Transaction already refunded');
        }

        $receiptTypeCode = 'R';
        $salesTypeCode =  'N';

        $customer = $this->validateCustomer($data, $company);

        $invoiceData = $this->buildEbmInvoice($data, $customer, $branch, $company, $receiptTypeCode, $salesTypeCode, $data->originalInvoiceNumber);
        $invoice =   $this->saveEbmTransaction($invoiceData, $branch, $user, $customer, TransactionTypeEnums::SALES_RETURN->value);
        return $invoice;
    }

    public function calculatePos($data, CompanyBranch $branch)
    {

        $calculatedData = $this->productService->calculateTax($data->items, $branch->id);
        $taxblAmtA = 0;
        $taxblAmtB = 0;
        $taxblAmtC = 0;
        $taxblAmtD = 0;
        $total = 0;
        $taxAmount = 0;
        foreach ($calculatedData as $product) {

            switch ($product->taxName) {
                case "A":
                    $taxblAmtA += $product->taxableAmount;
                    $total += $product->taxableAmount;
                    break;
                case "B":
                    $taxblAmtB += $product->taxableAmount;
                    $total += $product->taxableAmount;
                    $taxAmount += $product->taxAmount;
                    break;
                case "C":
                    $taxblAmtC += $product->taxableAmount;
                    $total += $product->taxableAmount;
                    break;
                case "D":
                    $taxblAmtD += $product->taxableAmount;
                    $total += $product->taxableAmount;
                    break;
                default:
                    $total += $product->taxableAmount;
                    break;
            }
        }

        $afterInsuranceDiscount = null;
        $insuranceDiscountAmount = null;
        $percentage = null;
        $insuranceName = null;
        if ($data->customerID) {

            $customer = $this->companyPartyService->partyById($data->customerID, $branch->company_id);

            if (!is_null($customer['patientDetails'])) {
                $percentage = $customer['patientDetails']['percentage'];
                $insuranceDiscountAmount = $total * $percentage / 100;
                $afterInsuranceDiscount = $total - $insuranceDiscountAmount;
                $insuranceName = $customer['patientDetails']['insuranceName'];
            }
        }

        return [
            'taxblAmtA' => $taxblAmtA,
            'taxblAmtB' => $taxblAmtB,
            'taxblAmtC' => $taxblAmtC,
            'taxblAmtD' => $taxblAmtD,
            'total' => $total,
            'totalTax' => $taxblAmtA + $taxblAmtB + $taxblAmtC + $taxblAmtD,
            'taxAmount' => $taxAmount,
            'afterInsuranceDiscount' => $afterInsuranceDiscount,
            'insuranceDiscount' => $insuranceDiscountAmount,
            'discountPercentage' => $percentage,
            'insuranceName' => $insuranceName
        ];
    }

    public function getRefundTransaction(int $id, CompanyBranch $branch): Order
    {

        $transaction = $this->getTransactionById($id, $branch->id);

        throw_if($transaction->isRefunded, NotFound::class, 'Transaction already refunded');

        return $transaction;
    }

    public function getTransaction(
        CompanyBranch $branch,
        string $invoiceType,
        int $perPage,
        int $page,
        ?int $userID = null,
        ?int $customerID = null,
        ?string $fromDate = null,
        ?string $toDate = null
    ): LengthAwarePaginator {

        $invoiceTypes = [
            TransactionTypeEnums::SALES->value,
            TransactionTypeEnums::SALES_RETURN->value,
            TransactionTypeEnums::PURCHASE->value,
            TransactionTypeEnums::PROFORMA->value,
            TransactionTypeEnums::DELIVERY_NOTE->value
        ];

        throw_if(!in_array($invoiceType, $invoiceTypes), NotFound::class, "Invalid invoice type, $invoiceType");

        return  Order::where('branch_id', $branch->id)
            ->where('transactionType', $invoiceType)
            ->withCount('items')
            ->when($customerID, fn($query) => $query->where('company_party_id', $customerID))
            ->when($userID, fn($query) => $query->where('user_id', $userID))
            ->FilterByDate($fromDate, $toDate)
            ->orderBy('created_at', 'desc')
            ->paginate(perPage: $perPage, page: $page)
            ->through(fn($transaction) => [
                'id' => $transaction->id,
                'invoiceNumber' => $transaction->invoiceNumber,
                'clientName' => $transaction->clientName,
                'salesDate' => $transaction->salesDate,
                'totalAmount' => $transaction->totAmt,
                'synced' => $transaction->synced,
                'isRefunded' => $transaction->isRefunded,
                'status' => $transaction->status,
                "itemsCount" => $transaction->items_count,
                "invoiceType" => $transaction->transactionType
            ]);
    }

    public function transactionById(int $transactionId, int $branchId): array
    {

        $transaction = $this->getTransactionById($transactionId, $branchId);

        throw_if(is_null($transaction), NotFound::class, 'Transaction not found');

        return [
            'id' => $transaction->id,
            'invoiceNumber' => $transaction->invoiceNumber,
            'originalInvoiceNumber' => $transaction->originalInvoiceNumber,
            'clientTin' => $transaction->clientTin,
            'clientName' => $transaction->clientName,
            'clientPhoneNumber' => $transaction->clientPhoneNumber,
            'salesTypeCode' => $transaction->salesTypeCode,
            'receiptTypeCode' => $transaction->receiptTypeCode,
            'paymentTypeCode' => $transaction->paymentTypeCode,
            'salesStatusCode' => $transaction->salesStatusCode,
            'confirmationDate' => $transaction->confirmationDate,
            'type' => $transaction->type,
            'isRefunded' => $transaction->isRefunded,
            'taxblAmtA' => $transaction->taxblAmtA,
            'taxblAmtB' => $transaction->taxblAmtB,
            'taxblAmtC' => $transaction->taxblAmtC,
            'taxblAmtD' => $transaction->taxblAmtD,
            'taxAmtA' => $transaction->taxAmtA,
            'taxAmtB' => $transaction->taxAmtB,
            'taxAmtC' => $transaction->taxAmtC,
            'taxAmtD' => $transaction->taxAmtD,
            'totTaxblAmt' => $transaction->totTaxblAmt,
            'totTaxAmt' => $transaction->totTaxAmt,
            'totAmt' => $transaction->totAmt,
            'salesDate' => $transaction->salesDate,
            'note' => $transaction->note,
            'items' => $transaction->items->map(fn($item) => [
                'productName' => $item->productName,
                'units' => $item->quantity,
                'unitPrice' => $item->price,
                'discountRate' => $item->discount,
                'taxAmount' => $item->taxAmount,
                'taxRate' => $item->taxRate,
                'taxName' => $item->taxName,
                'totalAmount' => $item->totalAmount,
                'totalDiscount' => $item->totalDiscount
            ])
        ];
    }

    public function getTransactionById(int $id, int $branchId): Order
    {

        $order =  Order::where('id', $id)
            ->where('branch_id', $branchId)
            ->with([
                'items',
                'signature',
                'party:id,name,phone,email,tin,address',
                'party.patientDetails:id,hasAffiliation,insurance_id,party_id,code,percentage,affiliationNumber,affiliateFirstName,affiliateLastName,relationship,beneficiaryFirstName,beneficiaryLastName,beneficiaryNumber,department,dateOfBirth',
                'party.patientDetails.insurance:id,name,code,rate',
                'user:id,name',
            ])
            ->first();

        throw_if(is_null($order), NotFound::class, 'Transaction not found');

        return $order;
    }

    public function buildEbmInvoice($data, $customer, $branch, Company $company, $receiptTypeCode, $salesTypeCode, $originalInvoiceNumber = 0)
    {

        $this->validateCustomer($data, $company);
        $paymentMode = $this->validatePaymentMode($data->paymentMethodID, $company);

        $payload = [

            "prescriptionNumber" => $data->prescriptionNumber,
            "insuranceTin" => $data->insuranceTin,
            "tin" => $company->tin,
            "bhfId" => $branch->branchCode,
            "invcNo" => nextInvoiceNo($branch->id), // todo
            "orgInvcNo" => $originalInvoiceNumber,
            "custNm" => $customer->name,
            "custTin" => $customer->tin,
            "prcOrdCd" => $data->purchaseCode,
            "salesTyCd" => $salesTypeCode,
            "rcptTyCd" => $receiptTypeCode,
            "pmtTyCd" => $paymentMode->code,
            "salesSttsCd" => "02",
            "cfmDt" => Carbon::now()->format("YmdHis"),
            "salesDt" => Carbon::now()->format("Ymd"),
            "totItemCnt" => count($data->items),
            "taxblAmtA" => 0,
            "taxblAmtB" => 0,
            "taxblAmtC" => 0,
            "taxblAmtD" => 0,
            "taxRtA" => Tax::wherecode("A")->first()->rate,
            "taxRtB" => Tax::wherecode("B")->first()->rate,
            "taxRtC" => Tax::wherecode("C")->first()->rate,
            "taxRtD" => Tax::wherecode("D")->first()->rate,
            "taxAmtA" => 0,
            "taxAmtB" => 0,
            "taxAmtC" => 0,
            "taxAmtD" => 0,
            "totTaxblAmt" => 0,
            "totTaxAmt" => 0,
            "totAmt" => 0,
            "prchrAcptcYn" => "N",
            "regrId" => "Admin",
            "regrNm" => "Admin",
            "modrId" => "Admin",
            "modrNm" => "Admin",
            "remark" => $data->note,
            "receipt" => [
                "rptNo" => 1,
                "trdeNm" => 'name', // Trade Name  Cahr =>  N = 20
                "adrs" =>  'address', // Address => Char => N => 200
                "topMsg" => 'name' . " \n " . $company->address . " \n  TIN : " . $company->tin . " \n " . 'topMessage',  // Top Message => Char => N => 20
                "btmMsg" =>  'bottomMessage', // Bottom  Message => Char => N => 20
                "prchrAcptcYn" => "N", // Whether Received Item or Not => Char => Y => 1
                "custMblNo" => $customer->phone, // Customer Mobile Number => Char => N => 20
            ],
            "itemList" => []
        ];

        if ($receiptTypeCode === 'R') {
            $payload["rfdRsnCd"] = "06";
        }

        $productData =   $this->productService->calculateTax($data->items, $branch->id);

        $itemSequence = 0;
        foreach ($productData as $product) {

            switch ($product->taxName) {
                case "A":
                    $payload["taxblAmtA"] += $product->taxableAmount;
                    break;
                case "B":
                    $payload["taxblAmtB"] +=  $product->taxableAmount;
                    $payload["taxAmtB"] += $product->taxAmount;
                    break;
                case "C":
                    $payload["taxblAmtC"] +=  $product->taxableAmount;
                    break;
                case "D":
                    $payload["taxblAmtD"] +=  $product->taxableAmount;
                    break;
            }

            $payload["totAmt"] +=  $product->taxableAmount;

            $itemSequence++;
            $payload["itemList"][] = [
                "itemSeq" => $itemSequence,
                "itemCd" => $product->code,
                "itemClsCd" => $product->class,
                "itemNm" => $product->name,
                "bcd" => '',
                "pkgUnitCd" => $product->packagingUnit,
                "pkg" => 1,
                "qtyUnitCd" => $product->quantityUnit,
                "qty" => $product->units,
                "itemExprDt" => null,
                "prc" =>  trimNumber($product->unitPrice),
                "dcAmt" => trimNumber($product->discountAmount),
                "dcRt" => trimNumber($product->discountRate),
                "splyAmt" => trimNumber($product->unitPrice * $product->units),
                "totDcAmt" => trimNumber($product->discountAmount),
                "taxblAmt" => trimNumber($product->taxableAmount),
                "taxTyCd" =>  $product->taxName,
                "taxAmt" => trimNumber($product->taxAmount),
                "totAmt" => trimNumber($product->taxableAmount)
            ];
        }

        $payload["totTaxblAmt"] = $payload["totAmt"];
        $payload["totTaxAmt"] = $payload["taxAmtB"];

        $payload["taxblAmtA"] = trimNumber($payload["taxblAmtA"]);
        $payload["taxblAmtB"] = trimNumber($payload["taxblAmtB"]);
        $payload["taxblAmtC"] = trimNumber($payload["taxblAmtC"]);
        $payload["taxblAmtD"] = trimNumber($payload["taxblAmtD"]);

        $payload["taxAmtA"] = trimNumber($payload["taxAmtA"]);
        $payload["taxAmtB"] = trimNumber($payload["taxAmtB"]);
        $payload["taxAmtC"] = trimNumber($payload["taxAmtC"]);
        $payload["taxAmtD"] = trimNumber($payload["taxAmtD"]);

        $payload["totTaxblAmt"] = trimNumber($payload["totTaxblAmt"]);
        $payload["totTaxAmt"] = trimNumber($payload["totTaxAmt"]);
        $payload["totAmt"] = trimNumber($payload["totAmt"]);

        return [
            'payload' => $payload,
            'productData' => $productData,
        ];
    }

    private function saveTransaction($data, $branch, $user, $receiptTypeCode, $salesTypeCode, $customer, $transactionType, $originalInvoiceNox = 0): int
    {

        $paymentModex = $this->validatePaymentMode($data->paymentMethodID, $branch->company);
        $productDatax =  $this->productService->calculateTax($data->items, $branch->id);

        try {

            DB::beginTransaction();

            $transactionPayload = [
                "totAmt" => 0,
                "invcNo" => nextInvoiceNo($branch->id),
                "orgInvcNo" => $originalInvoiceNox,
            ];

            foreach ($productDatax as $product) {
                $transactionPayload['totAmt'] += $product->taxableAmount;
            }

            $invoiceData = [
                'transaction' => $transactionPayload,
                'paymentMode' => $paymentModex,
                'productData' => $productDatax
            ];

            $transaction =  Order::create([
                "branch_id" => $branch->id,
                "user_id" => $user->id,
                "company_party_id" => $customer->id,
                "invoiceNumber" => $invoiceData['transaction']['invcNo'],
                "originalInvoiceNumber" => $invoiceData['transaction']['orgInvcNo'],
                "clientTin" => $customer->tin,
                "clientName" => $customer->name,
                "clientPhoneNumber" => $customer->phone,
                "salesTypeCode" => $salesTypeCode,
                "receiptTypeCode" => $receiptTypeCode,
                "paymentTypeCode" => $invoiceData['paymentMode']->code,
                "salesStatusCode" => null,
                "confirmationDate" => strtotime($data->transactionDate),
                "type" => $salesTypeCode . $receiptTypeCode,
                "salesDate" => strtotime($data->transactionDate),
                "remarks" => $data->note,
                "synced" => false,
                "isRefunded" => false,
                "demo" => false,

                "taxblAmtA" => 0,
                "taxblAmtB" => 0,
                "taxblAmtC" => 0,
                "taxblAmtD" => 0,

                "taxAmtA" => 0,
                "taxAmtB" => 0,
                "taxAmtC" => 0,
                "taxAmtD" => 0,

                "totTaxblAmt" => 0,
                "totTaxAmt" => 0,
                "totAmt" => $invoiceData['transaction']['totAmt'],
                "transactionType" => $transactionType
            ]);

            foreach ($invoiceData['productData'] as $product) {

                OrderItems::create([
                    "order_id" => $transaction->id,
                    "product_id" => $product->id,
                    "productName" => $product->name,
                    "productCode" => $product->code,
                    "batchNumber" => $product->batchNumber,
                    "expireDate" => $product->expireDate,
                    "quantity" => $product->units,
                    "remainingQuantity" => 0,
                    "price" => $product->unitPrice,
                    "discount" => $product->discountRate,
                    "supplyAmount" => $product->taxableAmount,
                    "taxAmount" => $product->taxAmount,
                    "taxRate" => $product->taxRate,
                    "taxName" => $product->taxName,
                    "totalAmount" => $product->taxableAmount,
                    "totalDiscount" => $product->discountAmount,
                ]);

                if ($receiptTypeCode == 'S' && $salesTypeCode != 'P') {
                    $itemData =  UpdateProductQuantityOutData::from([
                        'quantity' => $product->units,
                        'description' => 'Sales',
                        'batchNumber' => $product->batchNumber,
                        'expireDate' => $product->expireDate,
                        'movingUnit' => $product->movingUnit,
                        'orderType' => TransactionTypeEnums::SALES->value,
                        'partyId' => $customer->id,

                    ]);
                    $this->productService->reduceProductQuantity($itemData, $branch, $product->id, $user->id);
                } elseif ($receiptTypeCode == 'R') {
                    $itemData =  UpdateProductQuantityData::from([
                        'quantity' => $product->units,
                        'batchNumber' => $product->batchNumber,
                        'expireDate' => $product->expireDate,
                        'description' => 'Sales Return',
                        'movingUnit' => ProductMovingUnitEnums::SUB->value,
                        'orderType' => TransactionTypeEnums::SALES_RETURN->value,
                        'partyId' => $customer->id
                    ]);
                    $this->productService->increaseProductQuantity($itemData, $branch, $product->id, $user->id);
                }
            }

            if ($receiptTypeCode == 'S' || $receiptTypeCode == 'R' && $salesTypeCode != 'P') {

                $transactionAccountData = TransactionAccountData::from([

                    'branch_id' => $branch->id,
                    'payment_mode_id' => $invoiceData['paymentMode']->id,
                    'user_id' => $user->id,
                    'amount' =>  $transaction->totAmt,
                    'type' => $receiptTypeCode == 'S' ? TransactionAccountTypesEnum::CREDIT->value : TransactionAccountTypesEnum::DEBIT->value,
                    'sourceType' => $receiptTypeCode == 'S' ? TransactionTypeEnums::SALES->value : TransactionTypeEnums::SALES_RETURN->value,
                    'source_id' => $transaction->id,
                    'description' => $transaction->remarks,
                    'date' => $data->transactionDate
                ]);

                $this->transactionAccountService->createTransaction($transactionAccountData);
            }

            $logData = LogTrackData::from([
                'company_id' => $branch->company_id,
                'branch_id' => $branch->id,
                'user_id' => $user->id,
                'message' => 'Transaction Created, ID: ' . $transaction->id,
                'action' => LogTrackStatusEnums::CREATE->value,
            ]);

            $this->logService->createLog($logData);
            saveNextInvoiceNo($branch->id);
            DB::commit();

            return $transaction->id;
        } catch (\Exception $e) {

            DB::rollBack();
            Log::error($e->getMessage());
            throw_if(true, ServerError::class, 'Error saving transaction');
        }
    }

    private function saveEbmTransaction($data, $branch, $user, $customer, $transactionType): int
    {

        $invoiceSignature =   $this->ebmService->saveSaleTransaction($data, $branch->company, $branch);

        try {

            DB::beginTransaction();

            $payload = $data['payload'];

            $productData = $data['productData'];
            $transaction =  Order::create([
                "branch_id" => $branch->id,
                "user_id" => $user->id,
                "company_party_id" => $customer->id,
                "invoiceNumber" => $payload['invcNo'],
                "originalInvoiceNumber" => $payload['orgInvcNo'],
                "clientTin" => $payload['custTin'],
                "clientName" => $payload['custNm'],
                "clientPhoneNumber" => $payload['receipt']['custMblNo'],
                "salesTypeCode" => $payload['salesTyCd'],
                "receiptTypeCode" => $payload['rcptTyCd'],
                "paymentTypeCode" => $payload['pmtTyCd'],
                "salesStatusCode" => null,
                "confirmationDate" => strtotime($payload['cfmDt']),
                "type" => $payload['salesTyCd'] . $payload['rcptTyCd'],
                "salesDate" => strtotime($payload['salesDt']),
                "remarks" => $payload['remark'],
                "synced" => false,
                "isRefunded" => false,
                "demo" => false,

                "prescriptionNumber" => $payload['prescriptionNumber'],
                "insuranceTin" => $payload['insuranceTin'],

                "taxblAmtA" => $payload['taxblAmtA'],
                "taxblAmtB" => $payload['taxblAmtB'],
                "taxblAmtC" => $payload['taxblAmtC'],
                "taxblAmtD" => $payload['taxblAmtD'],

                "taxAmtA" => $payload['taxAmtA'],
                "taxAmtB" =>  $payload['taxAmtB'],
                "taxAmtC" => $payload['taxAmtC'],
                "taxAmtD" => $payload['taxAmtD'],

                "totTaxblAmt" => $payload['totTaxblAmt'],
                "totTaxAmt" => $payload['totTaxAmt'],
                "totAmt" => $payload['totAmt'],

                "transactionType" => $transactionType
            ]);

            foreach ($productData as $product) {

                OrderItems::create([
                    "order_id" => $transaction->id,
                    "product_id" => $product->id,
                    "productName" => $product->name,
                    "productCode" => $product->code,
                    "batchNumber" => $product->batchNumber,
                    "expireDate" => $product->expireDate,
                    "quantity" => $product->units,
                    "remainingQuantity" => 0,
                    "price" => $product->unitPrice,
                    "discount" => $product->discountRate,
                    "supplyAmount" => $product->taxableAmount,
                    "taxAmount" => $product->taxAmount,
                    "taxRate" => $product->taxRate,
                    "taxName" => $product->taxName,
                    "totalAmount" => $product->taxableAmount,
                    "totalDiscount" => $product->discountAmount,
                ]);

                if ($payload['rcptTyCd'] == 'S' && $payload['salesTyCd'] != 'P') {
                    $itemData =  UpdateProductQuantityOutData::from([
                        'quantity' => $product->units,
                        'description' => 'Sales',
                        'movingUnit' => $product->movingUnit,
                        'orderType' => TransactionTypeEnums::SALES->value,
                        "batchNumber" => $product->batchNumber,
                        "expireDate" => $product->expireDate,
                        'partyId' => $customer->id
                    ]);

                    $this->productService->reduceProductQuantity($itemData, $branch, $product->id, $user->id);
                } elseif ($payload['rcptTyCd'] == 'R') {
                    $itemData =  UpdateProductQuantityData::from([
                        'quantity' => $product->units,
                        'description' => 'Sales Return',
                        'movingUnit' => $product->movingUnit,
                        'orderType' => TransactionTypeEnums::SALES_RETURN->value,
                        "batchNumber" => $product->batchNumber,
                        "expireDate" => $product->expireDate,
                        "partyId" => $customer->id
                    ]);

                    $this->productService->increaseProductQuantity($itemData, $branch, $product->id, $user->id);
                }

                $stockMasterSaveData = StockMasterData::from([
                    'tin' => $payload['tin'],
                    'bhfId' => $payload['bhfId'],
                    'itemCd' => $product->code,
                    'rsdQty' => $product->units,
                    'regrId' => 'HiqAfrica',
                    'regrNm' => 'HiqAfrica',
                    'modrNm' => 'HiqAfrica',
                    'modrId' => 'HiqAfrica'
                ]);

                StockMasterSave::dispatch($stockMasterSaveData, $branch);
            }

            StockIoSave::dispatch($payload, $branch);

            if ($payload['rcptTyCd'] == 'S' || $payload['rcptTyCd'] == 'R' && $payload['salesTyCd'] != 'P') {

                $transactionAccountData = TransactionAccountData::from([

                    'branch_id' => $branch->id,
                    'payment_mode_id' => PaymentMode::where('code', $payload['pmtTyCd'])->first()->id,
                    'user_id' => $user->id,
                    'amount' => $payload['totAmt'],
                    'type' => $payload['rcptTyCd'] == 'S' ? TransactionAccountTypesEnum::CREDIT->value : TransactionAccountTypesEnum::DEBIT->value,
                    'sourceType' => $payload['rcptTyCd'] == 'S' ? TransactionTypeEnums::SALES->value : TransactionTypeEnums::SALES_RETURN->value,
                    'source_id' => $transaction->id,
                    'description' => $payload['remark'],
                    'date' => $payload['salesDt'],
                ]);

                TransactionAccountDataService::dispatch($transactionAccountData);
            }

            saveNextInvoiceNo($branch->id);

            DB::commit();

            OrderReceiptSignature::create([
                'order_id' => $transaction->id,
                'receiptNumber' => $invoiceSignature['data']['rcptNo'],
                'internalData' => $invoiceSignature['data']['intrlData'],
                'receiptSignature' => $invoiceSignature['data']['rcptSign'],
                'totalReceiptNumber' => $invoiceSignature['data']['totRcptNo'],
                'vsdcReceiptPublishDate' => strtotime($invoiceSignature['data']['vsdcRcptPbctDate']),
                'sdcId' => $invoiceSignature['data']['sdcId'],
                'mrcNumber' => $invoiceSignature['data']['mrcNo'],
                'invoiceNumber' => $transaction->invoiceNumber,
            ]);

            $logData = LogTrackData::from([
                'company_id' => $branch->company_id,
                'branch_id' => $branch->id,
                'user_id' => $user->id,
                'message' => 'Transaction Created, ID: ' . $transaction->id,
                'action' => LogTrackStatusEnums::CREATE->value,
            ]);

            $this->logService->createLog($logData);

            return $transaction->id;
        } catch (\Exception $e) {

            DB::rollBack();
            Log::error($e->getMessage());
            throw_if(true, ServerError::class, 'Error saving transaction');
        }
    }

    private function validateCustomer($data, Company $company): object
    {
        if (!is_null($data->customerID)) {

            $customerVisibility = CompanyParty::where('id', $data->customerID)->first();
            throw_if(is_null($customerVisibility), NotFound::class, 'Customer not found');

            if (is_null($customerVisibility->company_id)) {
                $customer = $this->companyPartyService->partyById($data->customerID, null);
            } else {
                $customer = $this->companyPartyService->partyById($data->customerID, $company->id);
            }

            if ($company->isEBM) {
                throw_if(!is_null($customer['tin']) && is_null($data->purchaseCode), NotFound::class, 'Purchase code is required');
            }
        } elseif (!is_null($data->customerTIN)) {

            if ($company->isEBM) {
                throw_if(is_null($data->purchaseCode), NotFound::class, 'Purchase code is required');
            }

            $customer = CompanyParty::where('company_id', $company->id)
                ->where('tin', $data->customerTIN)
                ->first();

            if (is_null($customer)) {

                $validatePartyType = PartyType::where('name', 'Customer')
                    ->whereCompanyId($company->id)
                    ->first();

                $customer = CompanyParty::create([
                    'name' => $data->customerName ?? 'Walkin Customer',
                    'tin' => $data->customerTIN,
                    'phone' => $data->customerPhone,
                    'company_id' => $company->id,
                    'slug' => 'walkin-customer',
                    'party_type_id' => $validatePartyType->id
                ]);
            }
        } else {

            $defaultCustomer = CompanyParty::where('company_id', null)
                ->where('slug', 'walkin-customer')
                ->where('name', 'Walkin Customer')
                ->first();

            $customer = $this->companyPartyService->partyById($defaultCustomer->id, null);
        }

        return (object) $customer;
    }

    private function validatePaymentMode(int $paymentMethodID, Company $company): PaymentMode
    {

        $paymentMode = PaymentMode::where('id', $paymentMethodID)
            ->whereCompanyId($company->id)
            ->when($company->isEBM, fn($query) => $query->where('forEbm', true))
            ->first();

        throw_if(is_null($paymentMode), NotFound::class, 'Payment method not found');

        return $paymentMode;
    }
}
